#pragma once

#include <gsl/gsl>

#include <QList>
#include <QObject>
#include <QString>
#include <QVariantMap>
#include <QQmlListProperty>
#include <qqmlintegration.h>

#include "models/types.h"
#include "models/message.h"
#include "models/attachment.h"

namespace ChamberUI::Services {

class MessageProcessor;
class ConversationManager;

} // namespace ChamberUI::Services

namespace ChamberUI::ViewModels {

using namespace ChamberUI::Services;
using namespace ChamberUI::Models;

class ChatViewModel : public QObject {
  Q_OBJECT
  QML_ELEMENT
  Q_PROPERTY(QVariantList messages READ messages NOTIFY messagesChanged)
  Q_PROPERTY(QString inputText READ inputText WRITE setInputText NOTIFY
                 inputTextChanged)
  Q_PROPERTY(bool isProcessing READ isProcessing NOTIFY isProcessingChanged)
  Q_PROPERTY(QString currentConversationId READ currentConversationId NOTIFY
                 currentConversationChanged)
  Q_PROPERTY(QVariantMap currentConversation READ currentConversation NOTIFY
                 currentConversationChanged)
  Q_PROPERTY(QVariantList conversationTemplates READ conversationTemplates
                 NOTIFY conversationTemplatesChanged)

public:
  ChatViewModel(gsl::not_null<QObject *> parent,
                gsl::not_null<MessageProcessor *> message_processor,
                gsl::not_null<ConversationManager *> conversation_manager);

  QVariantList messages() const;
  QString inputText() const;
  void setInputText(const QString &text);
  bool isProcessing() const;
  QString currentConversationId() const;
  [[nodiscard]]
  const Conversation &currentConversation() const;
  [[nodiscard]]
  Conversation &currentConversation();
  QVariantList conversationTemplates() const;

public slots:
  void sendMessage();
  void sendMessageWithAttachments(const QVariantList &attachments);
  void loadConversation(const QString &conversationId);
  void clearChat();
  void deleteMessage(const QString &messageId);
  void addAttachment(const QString &messageId, const QString &filePath,
                     const QString &fileType);
  void removeAttachment(const QString &messageId, const QString &attachmentId);

  // Conversation settings methods
  void saveConversationSettings(const QVariantMap &conversation);
  void createNewConversation(const QString &title, const QString &provider,
                             const QString &model, const QString &systemPrompt,
                             double temperature, int maxTokens);
  void createConversationFromTemplate(const QString &templateId);
  void saveAsTemplate(const QString &templateName);

signals:
  void messagesChanged();
  void inputTextChanged();
  void isProcessingChanged();
  void currentConversationChanged();
  void conversationTemplatesChanged();
  void messageSent(const QString &conversation_id, const QString &content);
  void attachmentAdded(const QString &message_id, const QString &attachment_id);
  void attachmentRemoved(const QString &message_id,
                         const QString &attachment_id);
  void conversationSettingsSaved();
  void conversationCreated(const QString &conversation_id);

private slots:
  void handleResponseReceived(const QString &conversation_id,
                              const QString &response);
  void handleErrorOccurred(const QString &conversation_id,
                           const QString &error);
  void handleProcessingStarted(const QString &conversation_id);
  void handleProcessingFinished(const QString &conversation_id);

private:
  QVariantList messages_;
  QString input_text_;
  bool is_processing_;
  QString current_conversation_id_;
  gsl::not_null<MessageProcessor *> message_processor_;
  gsl::not_null<ConversationManager *> conversation_manager_;

  void addMessage(const Message &message);
  void saveMessage(const Message &message);
  void processAttachments(Message &message,
                          const QList<Attachment *> &attachments);
};

} // namespace ChamberUI::ViewModels