#include "chat_view_model.h"

#include <QDateTime>
#include <QTimer>
#include <QUuid>
#include <QFileInfo>

#include "services/conversation_manager.h"
#include "services/message_processor.h"
#include "models/attachment_utility.h"

namespace ChamberUI::ViewModels {

ChatViewModel::ChatViewModel(
    gsl::not_null<QObject *> parent,
    gsl::not_null<MessageProcessor *> message_processor,
    gsl::not_null<ConversationManager *> conversation_manager)
    : QObject{parent}, is_processing_{false},
      message_processor_{message_processor},
      conversation_manager_{conversation_manager} {
  // Connect message processor signals
  connect(message_processor_, &MessageProcessor::responseReceived, this,
          &ChatViewModel::handleResponseReceived);
  connect(message_processor_, &MessageProcessor::errorOccurred, this,
          &ChatViewModel::handleErrorOccurred);
  connect(message_processor_, &MessageProcessor::processingStarted, this,
          &ChatViewModel::handleProcessingStarted);
  connect(message_processor_, &MessageProcessor::processingFinished, this,
          &ChatViewModel::handleProcessingFinished);
}

QVariantList ChatViewModel::messages() const { return messages_; }

QString ChatViewModel::inputText() const { return input_text_; }

void ChatViewModel::setInputText(const QString &text) {
  if (input_text_ != text) {
    input_text_ = text;
    emit inputTextChanged();
  }
}

bool ChatViewModel::isProcessing() const { return is_processing_; }

QString ChatViewModel::currentConversationId() const {
  return current_conversation_id_;
}

QVariantMap ChatViewModel::currentConversation() const {
  if (current_conversation_id_.isEmpty()) {
    return QVariantMap();
  }

  return conversation_manager_->getConversation(current_conversation_id_);
}

QVariantList ChatViewModel::conversationTemplates() const {
  QVariantList allConversations = conversation_manager_->getAllConversations();
  QVariantList templates;

  for (const QVariant &conversationVar : allConversations) {
    QVariantMap conversation = conversationVar.toMap();
    if (conversation["isTemplate"].toBool()) {
      templates.append(conversation);
    }
  }

  return templates;
}

void ChatViewModel::sendMessage() {
  if (input_text_.trimmed().isEmpty() || is_processing_) {
    return;
  }

  // Create user message
  QVariantMap userMessage;
  userMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
  userMessage["content"] = input_text_;
  userMessage["role"] = "user";
  userMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

  // Add to messages list
  addMessage(userMessage);

  // Save message to database
  saveMessage(userMessage);

  // Emit signal for message processing
  emit messageSent(current_conversation_id_, input_text_);

  // Clear input text
  setInputText("");

  // Get the current conversation settings
  QVariantMap conversationSettings = currentConversation();

  // Process the message using the message processor with conversation
  // settings
  message_processor_->processMessage(current_conversation_id_, input_text_,
                                     conversationSettings);
}

void ChatViewModel::sendMessageWithAttachments(
    const QVariantList &attachments) {
  if ((input_text_.trimmed().isEmpty() && attachments.isEmpty()) ||
      is_processing_) {
    return;
  }

  // Create user message
  QVariantMap userMessage;
  userMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
  userMessage["content"] = input_text_;
  userMessage["role"] = "user";
  userMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

  // Process attachments
  if (!attachments.isEmpty()) {
    processAttachments(userMessage, attachments);
  }

  // Add to messages list
  addMessage(userMessage);

  // Save message to database
  saveMessage(userMessage);

  // Emit signal for message processing
  emit messageSent(current_conversation_id_, input_text_);

  // Clear input text
  setInputText("");

  // Get the current conversation settings
  QVariantMap conversationSettings = currentConversation();

  // Process the message using the message processor with conversation
  // settings
  message_processor_->processMessage(current_conversation_id_, input_text_,
                                     conversationSettings);
}

void ChatViewModel::handleResponseReceived(const QString &conversation_id,
                                           const QString &response) {
  // Only handle responses for the current conversation
  if (conversation_id != current_conversation_id_) {
    return;
  }

  // Create AI response message
  QVariantMap aiMessage;
  aiMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
  aiMessage["content"] = response;
  aiMessage["role"] = "assistant";
  aiMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

  // Add to messages list
  addMessage(aiMessage);

  // Save message to database
  saveMessage(aiMessage);
}

void ChatViewModel::handleErrorOccurred(const QString &conversation_id,
                                        const QString &error) {
  // Only handle errors for the current conversation
  if (conversation_id != current_conversation_id_) {
    return;
  }

  // Create error message
  QVariantMap errorMessage;
  errorMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
  errorMessage["content"] = "Error: " + error;
  errorMessage["role"] = "system";
  errorMessage["timestamp"] =
      QDateTime::currentDateTime().toString(Qt::ISODate);

  // Add to messages list
  addMessage(errorMessage);
}

void ChatViewModel::handleProcessingStarted(const QString &conversation_id) {
  // Only handle events for the current conversation
  if (conversation_id != current_conversation_id_) {
    return;
  }

  is_processing_ = true;
  emit isProcessingChanged();
}

void ChatViewModel::handleProcessingFinished(const QString &conversation_id) {
  // Only handle events for the current conversation
  if (conversation_id != current_conversation_id_) {
    return;
  }

  is_processing_ = false;
  emit isProcessingChanged();
}

void ChatViewModel::loadConversation(const QString &conversationId) {
  if (current_conversation_id_ == conversationId) {
    return;
  }

  current_conversation_id_ = conversationId;

  // Clear current messages
  messages_.clear();

  // Load messages from the database
  QVariantList messages =
      conversation_manager_->getConversationMessages(conversationId);

  if (messages.isEmpty()) {
    // If no messages, add a welcome message
    QVariantMap welcomeMessage;
    welcomeMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
    welcomeMessage["content"] = "This is the beginning of your conversation.";
    welcomeMessage["role"] = "system";
    welcomeMessage["timestamp"] =
        QDateTime::currentDateTime().toString(Qt::ISODate);

    addMessage(welcomeMessage);
    saveMessage(welcomeMessage);
  } else {
    // Add all messages to the view
    for (const QVariant &messageVar : messages) {
      messages_.append(messageVar);
    }
    emit messagesChanged();
  }

  emit currentConversationChanged();
}

void ChatViewModel::saveConversationSettings(const QVariantMap &conversation) {
  if (conversation.isEmpty() || !conversation.contains("id")) {
    return;
  }

  // Save the conversation to the database
  // Use saveConversations() instead of saveConversation() which doesn't
  // exist
  conversation_manager_->saveConversations();

  // If this is the current conversation, update the provider and model
  if (conversation["id"].toString() == current_conversation_id_) {
    if (conversation.contains("provider")) {
      message_processor_->setProvider(conversation["provider"].toString());
    }

    // Update provider settings if needed
    QVariantMap providerSettings;
    if (conversation.contains("model")) {
      providerSettings["model"] = conversation["model"];
    }

    if (!providerSettings.isEmpty()) {
      message_processor_->setProviderSettings(providerSettings);
    }
  }

  emit conversationSettingsSaved();
}

void ChatViewModel::clearChat() {
  if (current_conversation_id_.isEmpty()) {
    return;
  }

  // Clear messages in the database
  conversation_manager_->clearConversation(current_conversation_id_);

  // Clear messages in the view
  messages_.clear();
  emit messagesChanged();

  // Add a system message indicating the chat was cleared
  QVariantMap clearMessage;
  clearMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
  clearMessage["content"] = "Chat history has been cleared.";
  clearMessage["role"] = "system";
  clearMessage["timestamp"] =
      QDateTime::currentDateTime().toString(Qt::ISODate);

  addMessage(clearMessage);
  saveMessage(clearMessage);
}

void ChatViewModel::deleteMessage(const QString &messageId) {
  // Find the message in the list
  for (int i = 0; i < messages_.size(); ++i) {
    QVariantMap message = messages_[i].toMap();
    if (message["id"].toString() == messageId) {
      // Remove from the list
      messages_.removeAt(i);
      emit messagesChanged();

      // Delete from database
      conversation_manager_->deleteMessage(current_conversation_id_, messageId);

      break;
    }
  }
}

void ChatViewModel::addAttachment(const QString &messageId,
                                  const QString &filePath,
                                  const QString &fileType) {
  // Find the message in the list
  for (int i = 0; i < messages_.size(); ++i) {
    QVariantMap message = messages_[i].toMap();
    if (message["id"].toString() == messageId) {
      // Add attachment to the message
      QString attachmentId = conversation_manager_->addAttachment(
          current_conversation_id_, messageId, filePath,
          fileType == "image"  ? Attachment::Image
          : fileType == "pdf"  ? Attachment::PDF
          : fileType == "text" ? Attachment::Text
                               : Attachment::Other);

      if (!attachmentId.isEmpty()) {
        // Reload the message to get updated attachments
        QVariantMap updatedMessage =
            conversation_manager_->getConversation(current_conversation_id_)
                .value("messages")
                .toList()
                .at(i)
                .toMap();

        messages_[i] = updatedMessage;
        emit messagesChanged();
        emit attachmentAdded(messageId, attachmentId);
      }

      break;
    }
  }
}

void ChatViewModel::removeAttachment(const QString &messageId,
                                     const QString &attachmentId) {
  // Find the message in the list
  for (int i = 0; i < messages_.size(); ++i) {
    QVariantMap message = messages_[i].toMap();
    if (message["id"].toString() == messageId) {
      // Remove attachment from the message
      bool success = conversation_manager_->removeAttachment(
          current_conversation_id_, messageId, attachmentId);

      if (success) {
        // Reload the message to get updated attachments
        QVariantMap updatedMessage =
            conversation_manager_->getConversation(current_conversation_id_)
                .value("messages")
                .toList()
                .at(i)
                .toMap();

        messages_[i] = updatedMessage;
        emit messagesChanged();
        emit attachmentRemoved(messageId, attachmentId);
      }

      break;
    }
  }
}

void ChatViewModel::processAttachments(QVariantMap &message,
                                       const QVariantList &attachments) {
  if (attachments.isEmpty()) {
    return;
  }

  QVariantList processedAttachments;

  for (const QVariant &attachmentVar : attachments) {
    QVariantMap attachment = attachmentVar.toMap();

    // Get file info
    QString filePath = attachment["path"].toString();
    QString fileName = attachment["filename"].toString();
    QString fileType = attachment["type"].toString();

    // Determine attachment type
    Attachment::Type type = Attachment::Other;
    if (fileType == "image") {
      type = Attachment::Image;
    } else if (fileType == "pdf") {
      type = Attachment::PDF;
    } else if (fileType == "text") {
      type = Attachment::Text;
    }

    // Save the attachment file to the storage location
    QString savedPath =
        conversation_manager_->saveAttachmentFile(filePath, fileName);
    if (!savedPath.isEmpty()) {
      // Create attachment object
      Attachment newAttachment;
      newAttachment.setFilename(fileName);
      newAttachment.setPath(savedPath);
      newAttachment.setType(type);

      // Add to processed attachments
      processedAttachments.append(newAttachment.toVariantMap());
    }
  }

  if (!processedAttachments.isEmpty()) {
    message["attachments"] = processedAttachments;
  }
}

void ChatViewModel::addMessage(const QVariantMap &message) {
  messages_.append(message);
  emit messagesChanged();
}

void ChatViewModel::saveMessage(const QVariantMap &message) {
  if (current_conversation_id_.isEmpty()) {
    return;
  }

  // Save message to the database using conversation manager
  conversation_manager_->addMessage(current_conversation_id_,
                                    message["content"].toString(),
                                    message["role"].toString());
}

void ChatViewModel::createNewConversation(const QString &title,
                                          const QString &provider,
                                          const QString &model,
                                          const QString &systemPrompt,
                                          double temperature, int maxTokens) {
  // Create a new conversation
  QString conversationId = conversation_manager_->createConversation(title);

  // Set conversation settings
  QVariantMap conversation =
      conversation_manager_->getConversation(conversationId);
  conversation["provider"] = provider;
  conversation["model"] = model;
  conversation["systemPrompt"] = systemPrompt;
  conversation["temperature"] = temperature;
  conversation["maxTokens"] = maxTokens;
  // Save the conversation
  // Use saveConversations() instead of saveConversation() which doesn't
  // exist
  conversation_manager_->saveConversations();

  // Load the new conversation
  loadConversation(conversationId);

  // Emit signal
  emit conversationCreated(conversationId);
}

void ChatViewModel::createConversationFromTemplate(const QString &templateId) {
  // Get the template
  QVariantMap templateConversation =
      conversation_manager_->getConversation(templateId);
  if (templateConversation.isEmpty() ||
      !templateConversation["isTemplate"].toBool()) {
    return;
  }

  // Create a new conversation with a title based on the template
  QString title = templateConversation["templateName"].toString();
  if (title.isEmpty()) {
    title = "New Conversation";
  }

  // Create the conversation
  QString conversationId = conversation_manager_->createConversation(title);

  // Copy settings from template
  QVariantMap newConversation =
      conversation_manager_->getConversation(conversationId);
  newConversation["provider"] = templateConversation["provider"];
  newConversation["model"] = templateConversation["model"];
  newConversation["systemPrompt"] = templateConversation["systemPrompt"];
  newConversation["temperature"] = templateConversation["temperature"];
  newConversation["maxTokens"] = templateConversation["maxTokens"];

  if (templateConversation.contains("settings")) {
    newConversation["settings"] = templateConversation["settings"];
  }

  // Save the conversation
  // Use saveConversations() instead of saveConversation() which doesn't
  // exist
  conversation_manager_->saveConversations();

  // Load the new conversation
  loadConversation(conversationId);

  // Emit signal
  emit conversationCreated(conversationId);
}

void ChatViewModel::saveAsTemplate(const QString &templateName) {
  if (current_conversation_id_.isEmpty()) {
    return;
  }

  // Get the current conversation
  QVariantMap conversation =
      conversation_manager_->getConversation(current_conversation_id_);
  if (conversation.isEmpty()) {
    return;
  }

  // Create a new conversation for the template
  QString templateId = conversation_manager_->createConversation(templateName);

  // Copy settings from current conversation
  QVariantMap templateConversation =
      conversation_manager_->getConversation(templateId);
  templateConversation["provider"] = conversation["provider"];
  templateConversation["model"] = conversation["model"];
  templateConversation["systemPrompt"] = conversation["systemPrompt"];
  templateConversation["temperature"] = conversation["temperature"];
  templateConversation["maxTokens"] = conversation["maxTokens"];

  if (conversation.contains("settings")) {
    templateConversation["settings"] = conversation["settings"];
  }

  // Mark as template
  templateConversation["isTemplate"] = true;
  templateConversation["templateName"] = templateName;

  // Save the template
  // Use saveConversations() instead of saveConversation() which doesn't
  // exist
  conversation_manager_->saveConversations();

  // Emit signal
  emit conversationTemplatesChanged();
}

} // namespace ChamberUI::ViewModels