import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1
import ChamberUI.ViewModels 1.0

Rectangle {
    id: inputArea

    // Properties
    required property ChatViewModel chatViewModel

    // Methods - now delegate to ChatViewModel
    function addAttachment(filePath, fileName, fileType, fileSize) {
        chatViewModel.addPendingAttachment(filePath, fileName, fileType, fileSize || 0);
    }

    function clearAttachments() {
        chatViewModel.clearPendingAttachments();
    }

    function removeAttachment(index) {
        chatViewModel.removePendingAttachment(index);
    }

    function sendMessage() {
        // Unified send method - ChatViewModel handles attachments internally
        chatViewModel.sendMessage();
    }

    color: root.primaryColor

    // Voice recorder component
    VoiceRecorder {
        id: voiceRecorder

        anchors.centerIn: parent
        isVisible: false
        z: 10

        onRecordingFinished: function (text) {
            textInput.text = text;
            chatViewModel.inputText = text;
        }
    }

    // Detect file drops
    DropArea {
        anchors.fill: parent

        onDropped: function (drop) {
            if (drop.hasUrls) {
                for (var i = 0; i < drop.urls.length; i++) {
                    var url = drop.urls[i].toString();
                    // Remove the "file://" prefix
                    var filePath = url.replace(/^(file:\/{2})/, "");

                    // On Windows, also remove the leading slash
                    if (Qt.platform.os === "windows") {
                        filePath = filePath.replace(/^\//, "");
                    }

                    // Get file info
                    var fileName = filePath.split('/').pop();
                    // Since FileInfo is not available, we'll use a default size
                    var fileSize = 0; // Default size when we can't determine it

                    // Determine file type
                    var fileType = "other";
                    if (fileName.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) {
                        fileType = "image";
                    } else if (fileName.match(/\.(pdf)$/i)) {
                        fileType = "pdf";
                    } else if (fileName.match(/\.(txt|md|rtf|html|css|js|json|xml)$/i)) {
                        fileType = "text";
                    }

                    addAttachment(filePath, fileName, fileType, fileSize);
                }
            }
        }
    }
    ColumnLayout {
        spacing: 8

        anchors {
            fill: parent
            margins: 16
        }

        // Attachment preview
        AttachmentPreview {
            Layout.fillWidth: true
            attachments: pendingAttachments

            onRemoveAttachment: function (index) {
                inputArea.removeAttachment(index);
            }
        }

        // Input field
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            border.color: root.borderColor
            border.width: 1
            color: root.secondaryColor
            radius: 8

            // Text area for input
            ScrollView {
                id: scrollView

                anchors {
                    fill: parent
                    margins: 8
                }
                TextArea {
                    id: textInput

                    background: null
                    color: root.textColor
                    placeholderText: "Type a message..."
                    placeholderTextColor: root.mutedTextColor

                    // Bind to view model
                    text: chatViewModel.inputText
                    wrapMode: TextEdit.Wrap

                    // Send on Enter (but allow Shift+Enter for new line)
                    Keys.onPressed: function (event) {
                        if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                            if (!event.modifiers & Qt.ShiftModifier) {
                                sendButton.clicked();
                                event.accepted = true;
                            }
                        }
                    }
                    onTextChanged: chatViewModel.inputText = text
                }
            }
        }

        // Button row
        RowLayout {
            Layout.fillWidth: true
            spacing: 8

            // Character count and attachment count
            Label {
                Layout.fillWidth: true
                color: root.mutedTextColor
                font.pixelSize: 12
                text: {
                    var text = textInput.text.length + " characters";
                    if (pendingAttachments.length > 0) {
                        text += " | " + pendingAttachments.length + " attachment" + (pendingAttachments.length > 1 ? "s" : "");
                    }
                    return text;
                }
                visible: textInput.text.length > 0 || pendingAttachments.length > 0
            }

            // Microphone button
            Button {
                id: microphoneButton

                implicitHeight: 36
                implicitWidth: 36

                // Icon
                contentItem: Text {
                    color: root.textColor
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    text: "🎤"
                    verticalAlignment: Text.AlignVCenter
                }

                onClicked: {
                    voiceRecorder.isVisible = true;
                }
            }

            // File picker button
            FilePickerButton {
                id: filePickerButton

                nameFilters: ["Image files (*.jpg *.jpeg *.png *.gif *.bmp *.webp)", "PDF files (*.pdf)", "Text files (*.txt *.md *.rtf)", "All files (*)"]

                onFileSelected: function (filePath, fileName) {
                    // Determine file type
                    var fileType = "other";
                    if (fileName.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) {
                        fileType = "image";
                    } else if (fileName.match(/\.(pdf)$/i)) {
                        fileType = "pdf";
                    } else if (fileName.match(/\.(txt|md|rtf|html|css|js|json|xml)$/i)) {
                        fileType = "text";
                    }

                    // Get file size
                    // Since FileInfo is not available, we'll use a default size
                    var fileSize = 0; // Default size when we can't determine it

                    addAttachment(filePath, fileName, fileType, fileSize);
                }
            }

            // Clear button
            Button {
                id: clearButton

                text: "Clear"
                visible: textInput.text.length > 0 || pendingAttachments.length > 0

                onClicked: {
                    textInput.clear();
                    clearAttachments();
                }
            }

            // Send button
            Button {
                id: sendButton

                enabled: (textInput.text.trim().length > 0 || pendingAttachments.length > 0) && !chatViewModel.isProcessing
                text: "Send"

                onClicked: {
                    sendMessageWithAttachments();
                }
            }
        }
    }
}