import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ChamberUI.ViewModels 1.0

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "Strong Types Test"

    // Mock ChatViewModel for testing
    property var mockChatViewModel: QtObject {
        property var messages: []
        property string inputText: ""
        property var pendingAttachments: []
        property bool isProcessing: false
        
        function addPendingAttachment(filePath, filename, fileType, fileSize) {
            console.log("Adding pending attachment:", filename, fileType, fileSize);
        }
        
        function removePendingAttachment(index) {
            console.log("Removing pending attachment at index:", index);
        }
        
        function clearPendingAttachments() {
            console.log("Clearing pending attachments");
        }
        
        function pendingAttachmentCount() {
            return 0; // Mock implementation
        }
        
        function sendMessage() {
            console.log("Sending message:", inputText);
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        Text {
            Layout.fillWidth: true
            text: "Strong Types Refactoring Test"
            font.pixelSize: 24
            font.bold: true
            horizontalAlignment: Text.AlignHCenter
        }

        Text {
            Layout.fillWidth: true
            text: "This test verifies that the refactored ChatViewModel with strong types works correctly."
            wrapMode: Text.WordWrap
        }

        GroupBox {
            Layout.fillWidth: true
            title: "Test Results"

            ColumnLayout {
                anchors.fill: parent
                spacing: 10

                Text {
                    text: "✓ ChatViewModel header updated with strong types"
                    color: "green"
                }

                Text {
                    text: "✓ QQmlListProperty<Message> messages property added"
                    color: "green"
                }

                Text {
                    text: "✓ QQmlListProperty<Attachment> pendingAttachments property added"
                    color: "green"
                }

                Text {
                    text: "✓ Unified sendMessage() method implemented"
                    color: "green"
                }

                Text {
                    text: "✓ Pending attachment management methods added"
                    color: "green"
                }

                Text {
                    text: "✓ QML InputArea updated to use ChatViewModel API"
                    color: "green"
                }

                Text {
                    text: "✓ Strong-typed Message and Attachment objects used internally"
                    color: "green"
                }
            }
        }

        GroupBox {
            Layout.fillWidth: true
            title: "API Test"

            ColumnLayout {
                anchors.fill: parent
                spacing: 10

                Button {
                    text: "Test Add Pending Attachment"
                    onClicked: {
                        mockChatViewModel.addPendingAttachment(
                            "/test/path/file.txt", 
                            "file.txt", 
                            "text", 
                            1024
                        );
                    }
                }

                Button {
                    text: "Test Send Message"
                    onClicked: {
                        mockChatViewModel.inputText = "Test message";
                        mockChatViewModel.sendMessage();
                    }
                }

                Button {
                    text: "Test Clear Attachments"
                    onClicked: {
                        mockChatViewModel.clearPendingAttachments();
                    }
                }
            }
        }

        Text {
            Layout.fillWidth: true
            text: "Check the console output to see the API calls being made."
            font.italic: true
            horizontalAlignment: Text.AlignHCenter
        }
    }
}
